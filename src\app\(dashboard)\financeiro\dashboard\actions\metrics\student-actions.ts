"use server";

/**
 * Server Actions para métricas de alunos
 */

import { DashboardActionResult, StudentMetrics, DateRange } from '../../types/dashboard-types';
import { getAuthenticatedClient } from '../shared/auth-utils';

/**
 * Busca métricas de alunos para um período
 */
export async function getStudentMetrics(
  currentRange: DateRange,
  previousRange?: DateRange
): Promise<DashboardActionResult<StudentMetrics>> {
  try {
    const { supabase, tenantId } = await getAuthenticatedClient();

    // Buscar alunos ativos (com matrículas ativas)
    const { data: activeStudentsData } = await supabase
      .from('memberships')
      .select('student_id, created_at')
      .eq('tenant_id', tenantId)
      .eq('status', 'active');

    // Buscar total de estudantes
    const { data: totalStudentsData } = await supabase
      .from('students')
      .select('id, created_at')
      .eq('tenant_id', tenantId);

    // Buscar matrículas canceladas no período
    const { data: canceledMembershipsData } = await supabase
      .from('memberships')
      .select('student_id, canceled_at')
      .eq('tenant_id', tenantId)
      .eq('status', 'canceled')
      .gte('canceled_at', currentRange.startDate.toISOString())
      .lte('canceled_at', currentRange.endDate.toISOString());

    // Buscar novos alunos no período (baseado na data de criação da matrícula)
    const { data: newStudentsData } = await supabase
      .from('memberships')
      .select('student_id, created_at')
      .eq('tenant_id', tenantId)
      .gte('created_at', currentRange.startDate.toISOString())
      .lte('created_at', currentRange.endDate.toISOString());

    // Buscar receita total para calcular LTV
    const { data: paymentsData } = await supabase
      .from('payments')
      .select('amount, student_id')
      .eq('tenant_id', tenantId)
      .eq('status', 'paid');

    const totalStudents = (totalStudentsData || []).length;
    const activeStudents = (activeStudentsData || []).length;
    const newStudents = (newStudentsData || []).length;
    const churnedStudents = (canceledMembershipsData || []).length;

    // Calcular taxa de retenção e churn
    const retentionRate = totalStudents > 0 ? (activeStudents / totalStudents) * 100 : 0;
    const churnRate = totalStudents > 0 ? (churnedStudents / totalStudents) * 100 : 0;

    // Calcular LTV médio (receita total / número de alunos únicos)
    const totalRevenue = (paymentsData || []).reduce((sum, payment) => sum + Number(payment.amount), 0);
    const uniquePayingStudents = new Set((paymentsData || []).map(p => p.student_id)).size;
    const averageLifetimeValue = uniquePayingStudents > 0 ? totalRevenue / uniquePayingStudents : 0;

    const metrics: StudentMetrics = {
      totalStudents,
      activeStudents,
      newStudents,
      churnedStudents,
      retentionRate,
      churnRate,
      averageLifetimeValue
    };

    return {
      success: true,
      data: metrics,
      message: 'Métricas de alunos obtidas com sucesso'
    };

  } catch (error) {
    return {
      success: false,
      error: `Erro interno: ${error instanceof Error ? error.message : 'Erro desconhecido'}`
    };
  }
}

/**
 * Busca métricas de alunos com comparação de períodos
 */
export async function getStudentMetricsWithComparison(
  currentRange: DateRange,
  previousRange: DateRange
): Promise<DashboardActionResult<{
  current: StudentMetrics;
  previous: StudentMetrics;
}>> {
  try {
    const [currentResult, previousResult] = await Promise.all([
      getStudentMetrics(currentRange),
      getStudentMetrics(previousRange)
    ]);

    if (!currentResult.success) {
      return { success: false, error: currentResult.error };
    }

    if (!previousResult.success) {
      return { success: false, error: previousResult.error };
    }

    return {
      success: true,
      data: {
        current: currentResult.data!,
        previous: previousResult.data!
      },
      message: 'Métricas de alunos com comparação obtidas com sucesso'
    };

  } catch (error) {
    return {
      success: false,
      error: `Erro interno: ${error instanceof Error ? error.message : 'Erro desconhecido'}`
    };
  }
}
