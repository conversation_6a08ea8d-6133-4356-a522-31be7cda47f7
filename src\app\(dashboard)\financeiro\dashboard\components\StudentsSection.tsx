"use client";

/**
 * Seção de Alunos e Retenção do Dashboard Financeiro
 * Exibe métricas de alunos ativos, taxa de retenção, churn rate e LTV
 */

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Users,
  UserPlus,
  UserMinus,
  TrendingUp,
  TrendingDown,
  DollarSign,
  AlertCircle,
  Activity,
  Target,
  Percent
} from 'lucide-react';

import { EnhancedKPICard } from './EnhancedKPICard';
import { TrendIndicator } from './TrendIndicator';
import { LoadingStates } from './LoadingStates';
import {
  StudentGrowthChart,
  StudentStatusChart,
  RetentionCohortChart,
  LTVSegmentChart
} from './charts';

import type {
  FinancialKPIs,
  DashboardData
} from '../types/dashboard-types';
import { formatCurrency, formatPercentage, formatNumber } from '../utils/dashboard-utils';

// ============================================================================
// INTERFACES
// ============================================================================

interface StudentsSectionProps {
  kpis: FinancialKPIs;
  data: DashboardData;
  loading?: boolean;
  error?: string | null;
  className?: string;
}

// ============================================================================
// COMPONENTE PRINCIPAL
// ============================================================================

export const StudentsSection: React.FC<StudentsSectionProps> = ({
  kpis,
  data,
  loading = false,
  error = null,
  className = ''
}) => {
  if (loading) {
    return <LoadingStates.Dashboard />;
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          {error}
        </AlertDescription>
      </Alert>
    );
  }

  const studentMetrics = data.studentMetrics;

  return (
    <div className={`space-y-6 ${className}`}>
      {/* KPIs de Alunos */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* Alunos Ativos */}
        <EnhancedKPICard
          title="Alunos Ativos"
          metric={kpis.activeStudents}
          icon={<Users className="h-5 w-5" />}
          description="Alunos com mensalidades em dia"
          className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950 dark:to-blue-900"
        />

        {/* Taxa de Retenção */}
        <Card className="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-950 dark:to-green-900">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Taxa de Retenção</CardTitle>
            <Target className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-700 dark:text-green-300">
              {formatPercentage(studentMetrics.retentionRate)}
            </div>
            <p className="text-xs text-green-600 dark:text-green-400">
              % de alunos que permanecem
            </p>
          </CardContent>
        </Card>

        {/* Churn Rate */}
        <Card className="bg-gradient-to-br from-red-50 to-red-100 dark:from-red-950 dark:to-red-900">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Churn Rate</CardTitle>
            <UserMinus className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-700 dark:text-red-300">
              {formatPercentage(studentMetrics.churnRate)}
            </div>
            <p className="text-xs text-red-600 dark:text-red-400">
              % de alunos que cancelam
            </p>
          </CardContent>
        </Card>

        {/* LTV */}
        <Card className="bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-950 dark:to-purple-900">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">LTV Médio</CardTitle>
            <DollarSign className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-700 dark:text-purple-300">
              {formatCurrency(studentMetrics.averageLifetimeValue)}
            </div>
            <p className="text-xs text-purple-600 dark:text-purple-400">
              Valor médio por aluno
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Métricas Detalhadas */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Resumo de Alunos */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              Resumo de Alunos
            </CardTitle>
            <CardDescription>
              Visão geral da base de alunos
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Total de Alunos</span>
                  <span className="font-medium">{formatNumber(studentMetrics.totalStudents)}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Alunos Ativos</span>
                  <span className="font-medium text-green-600">{formatNumber(studentMetrics.activeStudents)}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Novos Alunos</span>
                  <span className="font-medium text-blue-600">{formatNumber(studentMetrics.newStudents)}</span>
                </div>
              </div>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Cancelamentos</span>
                  <span className="font-medium text-red-600">{formatNumber(studentMetrics.churnedStudents)}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Taxa de Ativação</span>
                  <span className="font-medium">
                    {formatPercentage(studentMetrics.totalStudents > 0 ? 
                      (studentMetrics.activeStudents / studentMetrics.totalStudents) * 100 : 0)}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Receita por Aluno</span>
                  <span className="font-medium">{kpis.averageRevenuePerUser.formatted.current}</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Análise de Retenção */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Percent className="h-5 w-5" />
              Análise de Retenção
            </CardTitle>
            <CardDescription>
              Métricas de permanência e fidelidade
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Taxa de Retenção</span>
                <div className="flex items-center gap-2">
                  <span className="font-medium text-green-600">
                    {formatPercentage(studentMetrics.retentionRate)}
                  </span>
                  {studentMetrics.retentionRate >= 80 ? (
                    <TrendingUp className="h-4 w-4 text-green-500" />
                  ) : (
                    <TrendingDown className="h-4 w-4 text-red-500" />
                  )}
                </div>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Churn Rate</span>
                <div className="flex items-center gap-2">
                  <span className="font-medium text-red-600">
                    {formatPercentage(studentMetrics.churnRate)}
                  </span>
                  {studentMetrics.churnRate <= 20 ? (
                    <TrendingUp className="h-4 w-4 text-green-500" />
                  ) : (
                    <TrendingDown className="h-4 w-4 text-red-500" />
                  )}
                </div>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">LTV Médio</span>
                <span className="font-medium text-purple-600">
                  {formatCurrency(studentMetrics.averageLifetimeValue)}
                </span>
              </div>

              <div className="pt-2 border-t">
                <div className="text-xs text-muted-foreground mb-2">Status da Retenção</div>
                <div className="flex items-center gap-2">
                  {studentMetrics.retentionRate >= 80 ? (
                    <>
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <span className="text-sm text-green-600">Excelente</span>
                    </>
                  ) : studentMetrics.retentionRate >= 60 ? (
                    <>
                      <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                      <span className="text-sm text-yellow-600">Boa</span>
                    </>
                  ) : (
                    <>
                      <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                      <span className="text-sm text-red-600">Precisa Melhorar</span>
                    </>
                  )}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Gráficos de Análise */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Evolução de Alunos */}
        <StudentGrowthChart loading={loading} />

        {/* Status dos Alunos */}
        <StudentStatusChart
          data={{
            active: studentMetrics.activeStudents,
            paused: 0, // TODO: Implementar dados de alunos pausados
            canceled: studentMetrics.churnedStudents,
            total: studentMetrics.totalStudents
          }}
          loading={loading}
        />
      </div>

      {/* Gráficos Avançados */}
      <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
        {/* Análise de Retenção por Coorte */}
        <RetentionCohortChart loading={loading} />

        {/* LTV por Segmento */}
        <LTVSegmentChart loading={loading} />
      </div>

      {/* Observação sobre CAC */}
      <Alert>
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          <strong>Customer Acquisition Cost (CAC):</strong> Para calcular o CAC, seria necessário
          implementar o rastreamento de custos de marketing e aquisição de clientes.
          Atualmente, o sistema não possui essas informações estruturadas.
        </AlertDescription>
      </Alert>
    </div>
  );
};
