"use server";

/**
 * Server Actions para gráficos de alunos
 */

import { DashboardActionResult, DateRange } from '../../types/dashboard-types';
import { getAuthenticatedClient } from '../shared/auth-utils';

// ============================================================================
// TIPOS ESPECÍFICOS DOS GRÁFICOS
// ============================================================================

interface StudentGrowthData {
  month: string;
  activeStudents: number;
  newStudents: number;
  churnedStudents: number;
  totalStudents: number;
}

interface StudentStatusData {
  active: number;
  paused: number;
  canceled: number;
  total: number;
}

interface CohortData {
  period: string;
  month1: number;
  month2: number;
  month3: number;
  month6: number;
  month12: number;
  cohortSize: number;
}

interface LTVSegmentData {
  segment: string;
  ltv: number;
  studentCount: number;
  averageMonthlyRevenue: number;
  averageLifespan: number;
}

// ============================================================================
// GRÁFICO DE CRESCIMENTO DE ALUNOS
// ============================================================================

export async function getStudentGrowthChart(
  dateRange: DateRange
): Promise<DashboardActionResult<StudentGrowthData[]>> {
  try {
    const { supabase, tenantId } = await getAuthenticatedClient();

    // Para MVP, retornar dados mock
    // TODO: Implementar query real baseada em dados históricos
    const mockData: StudentGrowthData[] = [
      { month: 'Jan', activeStudents: 45, newStudents: 8, churnedStudents: 2, totalStudents: 47 },
      { month: 'Fev', activeStudents: 52, newStudents: 9, churnedStudents: 2, totalStudents: 54 },
      { month: 'Mar', activeStudents: 58, newStudents: 7, churnedStudents: 1, totalStudents: 60 },
      { month: 'Abr', activeStudents: 61, newStudents: 5, churnedStudents: 2, totalStudents: 64 },
      { month: 'Mai', activeStudents: 64, newStudents: 6, churnedStudents: 3, totalStudents: 67 },
      { month: 'Jun', activeStudents: 62, newStudents: 4, churnedStudents: 6, totalStudents: 65 },
    ];

    return {
      success: true,
      data: mockData,
      message: 'Dados de crescimento de alunos obtidos com sucesso'
    };

  } catch (error) {
    return {
      success: false,
      error: `Erro interno: ${error instanceof Error ? error.message : 'Erro desconhecido'}`
    };
  }
}

// ============================================================================
// GRÁFICO DE STATUS DOS ALUNOS
// ============================================================================

export async function getStudentStatusChart(): Promise<DashboardActionResult<StudentStatusData>> {
  try {
    const { supabase, tenantId } = await getAuthenticatedClient();

    // Buscar alunos por status
    const { data: membershipsData } = await supabase
      .from('memberships')
      .select('status')
      .eq('tenant_id', tenantId);

    const statusCounts = {
      active: 0,
      paused: 0,
      canceled: 0,
      expired: 0
    };

    (membershipsData || []).forEach(membership => {
      if (statusCounts.hasOwnProperty(membership.status)) {
        statusCounts[membership.status as keyof typeof statusCounts]++;
      }
    });

    const data: StudentStatusData = {
      active: statusCounts.active,
      paused: statusCounts.paused,
      canceled: statusCounts.canceled + statusCounts.expired,
      total: statusCounts.active + statusCounts.paused + statusCounts.canceled + statusCounts.expired
    };

    return {
      success: true,
      data,
      message: 'Dados de status dos alunos obtidos com sucesso'
    };

  } catch (error) {
    return {
      success: false,
      error: `Erro interno: ${error instanceof Error ? error.message : 'Erro desconhecido'}`
    };
  }
}

// ============================================================================
// GRÁFICO DE RETENÇÃO POR COORTE
// ============================================================================

export async function getRetentionCohortChart(): Promise<DashboardActionResult<CohortData[]>> {
  try {
    const { supabase, tenantId } = await getAuthenticatedClient();

    // Para MVP, retornar dados mock
    // TODO: Implementar análise real de coortes baseada em dados históricos
    const mockData: CohortData[] = [
      {
        period: 'Jan 2024',
        month1: 100,
        month2: 85,
        month3: 78,
        month6: 65,
        month12: 55,
        cohortSize: 20
      },
      {
        period: 'Fev 2024',
        month1: 100,
        month2: 88,
        month3: 82,
        month6: 70,
        month12: 0,
        cohortSize: 25
      },
      {
        period: 'Mar 2024',
        month1: 100,
        month2: 90,
        month3: 85,
        month6: 0,
        month12: 0,
        cohortSize: 18
      }
    ];

    return {
      success: true,
      data: mockData,
      message: 'Dados de retenção por coorte obtidos com sucesso'
    };

  } catch (error) {
    return {
      success: false,
      error: `Erro interno: ${error instanceof Error ? error.message : 'Erro desconhecido'}`
    };
  }
}

// ============================================================================
// GRÁFICO DE LTV POR SEGMENTO
// ============================================================================

export async function getLTVSegmentChart(): Promise<DashboardActionResult<LTVSegmentData[]>> {
  try {
    const { supabase, tenantId } = await getAuthenticatedClient();

    // Para MVP, retornar dados mock baseados em modalidades
    // TODO: Implementar cálculo real baseado em dados de pagamentos e modalidades
    const mockData: LTVSegmentData[] = [
      {
        segment: 'Jiu-Jitsu',
        ltv: 2400,
        studentCount: 35,
        averageMonthlyRevenue: 200,
        averageLifespan: 12
      },
      {
        segment: 'Muay Thai',
        ltv: 1800,
        studentCount: 28,
        averageMonthlyRevenue: 180,
        averageLifespan: 10
      },
      {
        segment: 'Boxe',
        ltv: 1500,
        studentCount: 22,
        averageMonthlyRevenue: 150,
        averageLifespan: 10
      },
      {
        segment: 'MMA',
        ltv: 2100,
        studentCount: 18,
        averageMonthlyRevenue: 210,
        averageLifespan: 10
      },
      {
        segment: 'Funcional',
        ltv: 1200,
        studentCount: 25,
        averageMonthlyRevenue: 120,
        averageLifespan: 10
      }
    ];

    return {
      success: true,
      data: mockData,
      message: 'Dados de LTV por segmento obtidos com sucesso'
    };

  } catch (error) {
    return {
      success: false,
      error: `Erro interno: ${error instanceof Error ? error.message : 'Erro desconhecido'}`
    };
  }
}

// ============================================================================
// FUNÇÃO CONSOLIDADA PARA TODOS OS GRÁFICOS
// ============================================================================

export async function getAllStudentCharts(
  dateRange: DateRange
): Promise<DashboardActionResult<{
  growth: StudentGrowthData[];
  status: StudentStatusData;
  cohort: CohortData[];
  ltv: LTVSegmentData[];
}>> {
  try {
    const [growthResult, statusResult, cohortResult, ltvResult] = await Promise.all([
      getStudentGrowthChart(dateRange),
      getStudentStatusChart(),
      getRetentionCohortChart(),
      getLTVSegmentChart()
    ]);

    if (!growthResult.success) {
      return { success: false, error: growthResult.error };
    }
    if (!statusResult.success) {
      return { success: false, error: statusResult.error };
    }
    if (!cohortResult.success) {
      return { success: false, error: cohortResult.error };
    }
    if (!ltvResult.success) {
      return { success: false, error: ltvResult.error };
    }

    return {
      success: true,
      data: {
        growth: growthResult.data!,
        status: statusResult.data!,
        cohort: cohortResult.data!,
        ltv: ltvResult.data!
      },
      message: 'Todos os gráficos de alunos obtidos com sucesso'
    };

  } catch (error) {
    return {
      success: false,
      error: `Erro interno: ${error instanceof Error ? error.message : 'Erro desconhecido'}`
    };
  }
}
