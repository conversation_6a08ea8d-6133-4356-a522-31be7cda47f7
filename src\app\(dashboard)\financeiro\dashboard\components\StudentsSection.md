# Seção de Alunos e Retenção - Dashboard Financeiro

## 📊 Visão Geral

A seção de Alunos e Retenção fornece métricas essenciais para acompanhar a saúde da base de alunos da academia, incluindo taxa de retenção, churn rate e lifetime value (LTV).

## 🎯 Métricas Implementadas

### 1. Alunos Ativos
- **Definição**: Alunos com mensalidades em dia (status = 'active' na tabela memberships)
- **Cálculo**: COUNT(DISTINCT student_id) WHERE status = 'active'
- **Importância**: Indica a base ativa de receita recorrente

### 2. Taxa de Retenção
- **Definição**: Percentual de alunos que permanecem ativos
- **Cálculo**: (Alunos Ativos / Total de Alunos) × 100
- **Benchmark**: 
  - Excelente: ≥ 80%
  - Boa: 60-79%
  - Precisa Melhorar: < 60%

### 3. Churn Rate
- **Definição**: Percentual de alunos que cancelam no período
- **Cálculo**: (Alunos Cancelados / Total de Alunos) × 100
- **Importância**: Indica problemas de satisfação ou qualidade

### 4. Lifetime Value (LTV)
- **Definição**: Valor médio que um aluno gera ao longo do tempo
- **Cálculo**: Receita Total / Número de Alunos Únicos Pagantes
- **Uso**: Determinar investimento em aquisição e retenção

### 5. Customer Acquisition Cost (CAC)
- **Status**: Não implementado
- **Motivo**: Requer dados de custos de marketing não disponíveis no sistema atual
- **Recomendação**: Implementar rastreamento de campanhas e custos de aquisição

## 🏗️ Estrutura de Componentes

### StudentsSection.tsx
Componente principal que exibe todas as métricas de alunos:

```tsx
interface StudentsSectionProps {
  kpis: FinancialKPIs;
  data: DashboardData;
  loading?: boolean;
  error?: string | null;
  className?: string;
}
```

### Subcomponentes
- **KPI Cards**: Exibem métricas principais com indicadores visuais
- **Resumo de Alunos**: Tabela com detalhes da base de alunos
- **Análise de Retenção**: Métricas de permanência e fidelidade

## 📊 Dados Utilizados

### Tabelas do Banco
1. **students**: Base de alunos cadastrados
2. **memberships**: Matrículas e status dos alunos
3. **payments**: Pagamentos para cálculo do LTV
4. **membership_status_logs**: Histórico de mudanças de status

### Queries Principais
```sql
-- Alunos ativos
SELECT COUNT(DISTINCT student_id) 
FROM memberships 
WHERE status = 'active' AND tenant_id = ?

-- Taxa de retenção
SELECT 
  COUNT(DISTINCT s.id) as total_students,
  COUNT(DISTINCT CASE WHEN m.status = 'active' THEN s.id END) as active_students
FROM students s
LEFT JOIN memberships m ON s.id = m.student_id
WHERE s.tenant_id = ?

-- LTV médio
SELECT 
  SUM(amount) as total_revenue,
  COUNT(DISTINCT student_id) as unique_students
FROM payments 
WHERE status = 'paid' AND tenant_id = ?
```

## 🎨 Design e UX

### Cores e Indicadores
- **Verde**: Métricas positivas (retenção, alunos ativos)
- **Vermelho**: Métricas de atenção (churn, cancelamentos)
- **Azul**: Métricas neutras (total de alunos)
- **Roxo**: Métricas financeiras (LTV)

### Responsividade
- **Desktop**: Grid 4 colunas para KPIs
- **Tablet**: Grid 2 colunas
- **Mobile**: Grid 1 coluna empilhado

## 🔧 Implementação Técnica

### Server Actions
```typescript
// student-actions.ts
export async function getStudentMetrics(
  currentRange: DateRange,
  previousRange?: DateRange
): Promise<DashboardActionResult<StudentMetrics>>
```

### Tipos TypeScript
```typescript
interface StudentMetrics {
  totalStudents: number;
  activeStudents: number;
  newStudents: number;
  churnedStudents: number;
  retentionRate: number;
  churnRate: number;
  averageLifetimeValue: number;
}
```

## 📈 Métricas Futuras

### Próximas Implementações
1. **Cohort Analysis**: Análise de coortes por período de entrada
2. **Segmentação**: Métricas por modalidade, plano ou faixa etária
3. **Previsões**: Projeções de churn e crescimento
4. **CAC**: Implementar rastreamento de custos de aquisição

### Melhorias Sugeridas
1. **Histórico**: Gráficos de evolução temporal das métricas
2. **Alertas**: Notificações para métricas críticas
3. **Comparações**: Benchmarks com períodos anteriores
4. **Drill-down**: Detalhamento por aluno individual

## 🚨 Limitações Atuais

### Dados Não Disponíveis
- **CAC**: Custos de marketing e aquisição
- **Tempo de Vida**: Duração média das matrículas
- **Motivos de Cancelamento**: Razões específicas do churn
- **Sazonalidade**: Padrões temporais de entrada/saída

### Cálculos Simplificados
- **Churn Rate**: Baseado apenas em cancelamentos no período
- **LTV**: Média simples, não considera tempo de vida
- **Novos Alunos**: Baseado em data de criação da matrícula

## 🔍 Monitoramento

### Métricas de Alerta
- Taxa de retenção < 60%
- Churn rate > 20%
- Queda de 10%+ em alunos ativos
- LTV em declínio por 2+ períodos

### Ações Recomendadas
1. **Retenção Baixa**: Revisar qualidade das aulas e atendimento
2. **Churn Alto**: Implementar pesquisas de satisfação
3. **LTV Baixo**: Revisar estratégia de preços e upselling
4. **Poucos Novos Alunos**: Intensificar marketing e indicações

## 📚 Referências

- [Customer Lifetime Value](https://blog.hubspot.com/service/what-does-cltv-mean)
- [Churn Rate Benchmarks](https://blog.hubspot.com/service/what-does-cltv-mean)
- [Retention Rate Best Practices](https://blog.hubspot.com/service/what-does-cltv-mean)
